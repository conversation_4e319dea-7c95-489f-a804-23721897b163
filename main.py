import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os

import threading
import time
import logging

from app.routers import resume, auth, feedback, free_template, idphoto, error_report
from app.database import get_pool_status
from config.fastapi_config import settings, validate_config

# 验证配置
validate_config()

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(settings.LOG_FILE) if settings.LOG_FILE else logging.NullHandler()
    ]
)

app = FastAPI(
    title=settings.APP_NAME,
    description="微信小程序简历服务后端API",
    version=settings.VERSION
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=settings.CORS_CREDENTIALS,
    allow_methods=settings.CORS_METHODS,
    allow_headers=settings.CORS_HEADERS,
)

# 创建静态文件目录（如果不存在）
static_dir = os.path.join(os.path.dirname(__file__), settings.STATIC_FILES_DIR)
if not os.path.exists(static_dir):
    os.makedirs(static_dir)

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# 定时清理临时文件的后台任务
def cleanup_temp_files_periodically():
    """定期清理临时文件的后台任务"""
    import logging
    from app.services.temp_file_service import get_temp_file_service

    logger = logging.getLogger(__name__)
    temp_service = get_temp_file_service()

    while True:
        try:
            time.sleep(settings.CLEANUP_INTERVAL_HOURS * 3600)  # 使用配置的清理间隔
            temp_service.cleanup_expired_files()
            logger.info("定时清理临时文件完成")
        except Exception as e:
            logger.error(f"定时清理临时文件失败: {str(e)}")

# 启动后台清理任务
cleanup_thread = threading.Thread(target=cleanup_temp_files_periodically, daemon=True)
cleanup_thread.start()

# 添加路由
app.include_router(resume.router)
app.include_router(auth.router)
app.include_router(feedback.router)
app.include_router(free_template.router)
app.include_router(idphoto.router)
app.include_router(error_report.router)

@app.get("/")
async def root():
    return {"message": "欢迎使用FastAPI服务"}

@app.get("/health/database")
async def database_health():
    """数据库连接池健康检查"""
    try:
        pool_status = get_pool_status()
        return {
            "status": "healthy",
            "pool_info": pool_status,
            "timestamp": time.time()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": time.time()
        }

if __name__ == "__main__":
    uvicorn.run("main:app", host=settings.HOST, port=settings.PORT, reload=settings.DEBUG, workers=settings.WORKERS)
