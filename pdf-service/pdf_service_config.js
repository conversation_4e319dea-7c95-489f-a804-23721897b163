/**
 * PDF服务配置文件
 * 统一管理PDF服务相关的所有配置项
 */

require('dotenv').config({ path: require('path').join(__dirname, '.env') });

class PDFServiceConfig {
    constructor() {
        // ===========================================
        // 环境配置
        // ===========================================
        this.NODE_ENV = process.env.NODE_ENV || 'development';
        this.DEBUG = process.env.DEBUG === 'true';

        // ===========================================
        // 服务器配置
        // ===========================================
        this.HOST = process.env.PDF_SERVICE_HOST || 'localhost';
        this.PORT = parseInt(process.env.PDF_SERVICE_PORT) || 13000;

        // ===========================================
        // 浏览器池配置
        // ===========================================
        this.CHROME_PATH = process.env.CHROME_PATH || '';
        //'/home/<USER>/.cache/puppeteer/chrome-headless-shell/linux-136.0.7103.92/chrome-headless-shell-linux64/chrome-headless-shell';


        this.BROWSER_POOL = {
            initialSize: parseInt(process.env.PDF_BROWSER_POOL_INITIAL_SIZE) || 2,
            maxBrowsers: parseInt(process.env.PDF_BROWSER_POOL_MAX_BROWSERS) || 4,
            maxPagesPerBrowser: parseInt(process.env.PDF_BROWSER_POOL_MAX_PAGES_PER_BROWSER) || 5,
            maxTotalUsage: parseInt(process.env.PDF_BROWSER_POOL_MAX_TOTAL_USAGE) || 100,
            maxAvgUsage: parseInt(process.env.PDF_BROWSER_POOL_MAX_AVG_USAGE) || 10,
            intervalHealthCheckCreateNewBrowser: parseInt(process.env.PDF_BROWSER_POOL_HEALTH_CHECK_INTERVAL) || 15000
        };

        // ===========================================
        // Puppeteer配置
        // ===========================================
        this.PUPPETEER = {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--disable-gpu',
                '--js-flags=--expose-gc',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        };

        // ===========================================
        // 性能配置
        // ===========================================
        this.PERFORMANCE = {
            requestBodySizeLimit: process.env.REQUEST_BODY_SIZE_LIMIT || '50mb',
            maxRetries: 3,
            retryDelay: 1000,
            pageTimeout: 30000,
            navigationTimeout: 30000
        };

        // ===========================================
        // 文件配置
        // ===========================================
        this.FILES = {
            tempDir: process.env.TEMP_FILES_DIR || 'temp',
            cleanupInterval: (parseInt(process.env.CLEANUP_INTERVAL_HOURS) || 1) * 60 * 60 * 1000,
            maxFileAge: (parseInt(process.env.TEMP_FILES_EXPIRE_HOURS) || 24) * 60 * 60 * 1000
        };

        // ===========================================
        // CORS配置
        // ===========================================
        this.CORS = {
            origin: process.env.CORS_ORIGINS ? process.env.CORS_ORIGINS.split(',') : '*',
            credentials: process.env.CORS_CREDENTIALS === 'true',
            methods: process.env.CORS_METHODS ? process.env.CORS_METHODS.split(',') : ['GET', 'POST', 'PUT', 'DELETE'],
            allowedHeaders: process.env.CORS_HEADERS ? process.env.CORS_HEADERS.split(',') : '*'
        };

        // ===========================================
        // 日志配置
        // ===========================================
        this.LOGGING = {
            level: process.env.LOG_LEVEL || 'INFO',
            file: process.env.LOG_FILE || 'logs/pdf-service.log',
            enableConsole: this.NODE_ENV === 'development'
        };
    }

    /**
     * 验证配置
     */
    validate() {
        const warnings = [];

        if (this.NODE_ENV === 'production' && this.DEBUG) {
            warnings.push('警告: 生产环境不应启用DEBUG模式');
        }

        if (this.BROWSER_POOL.maxBrowsers < this.BROWSER_POOL.initialSize) {
            warnings.push('警告: maxBrowsers应该大于或等于initialSize');
        }

        if (this.PORT < 1024 && process.getuid && process.getuid() !== 0) {
            warnings.push(`警告: 端口${this.PORT}可能需要管理员权限`);
        }

        warnings.forEach(warning => console.warn(warning));
        return warnings.length === 0;
    }

    /**
     * 获取服务器URL
     */
    getServerUrl() {
        return `http://${this.HOST}:${this.PORT}`;
    }

    /**
     * 打印配置信息
     */
    printConfig() {
        console.log('===========================================');
        console.log('PDF服务配置信息');
        console.log('===========================================');
        console.log(`环境: ${this.NODE_ENV}`);
        console.log(`服务地址: ${this.getServerUrl()}`);
        console.log(`浏览器池配置:`);
        console.log(`  - 初始大小: ${this.BROWSER_POOL.initialSize}`);
        console.log(`  - 最大浏览器数: ${this.BROWSER_POOL.maxBrowsers}`);
        console.log(`  - 每个浏览器最大页面数: ${this.BROWSER_POOL.maxPagesPerBrowser}`);
        console.log(`调试模式: ${this.DEBUG ? '启用' : '禁用'}`);
        console.log('===========================================');
    }
}

// 创建配置实例
const config = new PDFServiceConfig();

// 验证配置
config.validate();

module.exports = config;
