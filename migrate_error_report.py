#!/usr/bin/env python3
"""
错误上报表数据库迁移脚本
"""
import mysql.connector
from mysql.connector import Error
import logging
import sys
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def check_table_exists(connection, table_name):
    """检查表是否存在"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = '{DB_CONFIG['database']}' 
            AND table_name = '{table_name}'
        """)
        result = cursor.fetchone()
        cursor.close()
        return result[0] > 0
    except Error as e:
        logger.error(f"检查表存在性失败: {e}")
        return False

def create_error_report_table(connection):
    """创建错误上报表"""
    try:
        cursor = connection.cursor()
        
        # 创建错误上报表
        create_table_sql = """
        CREATE TABLE error_reports (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            user_id INT NULL COMMENT '用户ID，可为空（未登录用户）',
            openid VARCHAR(100) NULL COMMENT '微信openid，可为空',
            error_type VARCHAR(100) NOT NULL COMMENT '错误类型',
            error_message TEXT NOT NULL COMMENT '错误信息',
            error_stack TEXT NULL COMMENT '错误堆栈',
            page_path VARCHAR(255) NULL COMMENT '发生错误的页面路径',
            user_agent TEXT NULL COMMENT '用户代理信息',
            device_info JSON NULL COMMENT '设备信息',
            app_version VARCHAR(50) NULL COMMENT '应用版本',
            system_info JSON NULL COMMENT '系统信息',
            network_type VARCHAR(50) NULL COMMENT '网络类型',
            timestamp TIMESTAMP NOT NULL COMMENT '错误发生时间',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上报时间',
            
            INDEX idx_id (id),
            INDEX idx_user_id (user_id),
            INDEX idx_openid (openid),
            INDEX idx_error_type (error_type),
            INDEX idx_error_type_created (error_type, created_at),
            INDEX idx_user_created (user_id, created_at),
            INDEX idx_openid_created (openid, created_at),
            INDEX idx_timestamp_created (timestamp, created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='错误上报表';
        """
        
        cursor.execute(create_table_sql)
        connection.commit()
        logger.info("错误上报表创建成功")
        
        cursor.close()
        return True
        
    except Error as e:
        logger.error(f"创建错误上报表失败: {e}")
        connection.rollback()
        return False

def add_foreign_key_constraint(connection):
    """添加外键约束（可选）"""
    try:
        cursor = connection.cursor()
        
        # 检查users表是否存在
        if check_table_exists(connection, 'users'):
            # 添加外键约束
            alter_sql = """
            ALTER TABLE error_reports 
            ADD CONSTRAINT fk_error_reports_user_id 
            FOREIGN KEY (user_id) REFERENCES users(id) 
            ON DELETE SET NULL ON UPDATE CASCADE
            """
            cursor.execute(alter_sql)
            connection.commit()
            logger.info("外键约束添加成功")
        else:
            logger.warning("users表不存在，跳过外键约束添加")
        
        cursor.close()
        return True
        
    except Error as e:
        logger.error(f"添加外键约束失败: {e}")
        connection.rollback()
        return False

def main():
    """主函数"""
    logger.info("开始错误上报表迁移...")
    
    # 获取数据库连接
    connection = get_db_connection()
    if not connection:
        logger.error("无法连接到数据库，迁移失败")
        sys.exit(1)
    
    try:
        # 检查表是否已存在
        if check_table_exists(connection, 'error_reports'):
            logger.info("错误上报表已存在，跳过创建")
        else:
            # 创建错误上报表
            if not create_error_report_table(connection):
                logger.error("创建错误上报表失败")
                sys.exit(1)
        
        # 添加外键约束（可选）
        try:
            add_foreign_key_constraint(connection)
        except Exception as e:
            logger.warning(f"添加外键约束失败，但不影响主要功能: {e}")
        
        logger.info("错误上报表迁移完成")
        
    except Exception as e:
        logger.exception("迁移过程中发生异常")
        sys.exit(1)
    
    finally:
        if connection.is_connected():
            connection.close()
            logger.info("数据库连接已关闭")

if __name__ == "__main__":
    main()
