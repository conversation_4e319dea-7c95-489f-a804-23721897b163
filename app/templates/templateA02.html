<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    {{ resume.basicInfo.name }}的简历
  </title>
  <link rel="stylesheet" href="{{ base_url|default('') }}/static/fontawesome/css/all.min.css">
  <style>
    /* 全局样式 */
    :root {
      /* 动态主题配置变量 */
      --theme-color: {{ theme_color|default('#44546b') }};
      --base-font-size: {{ base_font_size|default(11) }}pt;
      --max-font-size: {{ max_font_size|default(15) }}pt;
      --spacing: {{ spacing|default(1.5) }};
      --text-color: #000000;
      --secondary-color: #e44c4c;
    }


    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background-color: #ffffff;
      display: flex;
      justify-content: center;
      padding: 0px;
      overflow-x: hidden; /* 防止水平滚动 */
      font-family: "Source Han Sans SC VF", sans-serif;
    }

    .resume-container {
      width: 210mm;
      /* min-height: 297mm; */
      background-color: white;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      overflow: hidden; /* 防止内容溢出 */
      font-size: var(--base-font-size);
      line-height: var(--spacing);
    }

    .resume-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: var(--theme-color);
      padding: 10px 20px;
      width: 100%;
    }

    /* SVG图标的通用样式 */
    .header-icons svg{
      filter: brightness(0) invert(1);
    }


    /* 为左侧栏的图标设置黑色 */
    .left-column-title svg.icon-1,
    .left-column-title img.icon-1 {
      width: 20px;
      height: 20px;
      margin-right: 10px;
      filter: none;
      color: black; /* 为使用currentColor的图标设置颜色 */
    }

    /* 图标尺寸 */
    .header-icons .icon-1 {
      width: 26px;
      height: 26px;
      color: white;
      vertical-align: middle;
      margin-right: 10px;
    }

    .header-icons .icon-2 {
      width: 20px;
      height: 20px;
      vertical-align: middle;
      color: white;
      margin-right: 10px;
    }

    .header-icons .icon-3 {
      width: 14px;
      height: 14px;
      vertical-align: middle;
      color: white;
      margin-right: 10px;
    }

    .header-icons .icon-4 {
      width: 8px;
      height: 8px;
      vertical-align: middle;
      color: white;
      margin-right: 10px;
    }

    .left-column .icon-1 {
      width: 26px;
      height: 26px;
      margin-right: 10px;
    }

    .content {
      display: grid;
      grid-template-columns: 2.5fr 7.5fr; /* 左右分栏设为3:7比例 */
      width: 100%;
      min-height: 0; /* 修复IE中的grid内容溢出问题 */
    }

    /* 左侧栏样式 */
    .left-column {
      background-color: #f0f0f0;
      padding: 0 10px 20px;
      overflow: hidden; /* 防止内容溢出 */
      box-sizing: border-box; /* 确保padding不会增加宽度 */
      height: 100%;
      /* min-height: calc(297mm - 45px); A4页面高度减去标题高度 */
      position: relative; /* 添加相对定位 */
    }

    /* 更新左侧栏图标样式 */
    .left-column .icon-1 {
      width: 26px;
      height: 26px;
      margin-right: 10px;
    }

    .photo-container {
      padding: 15px;
      margin: 15px;
      display: flex;
      justify-content: center;
    }

    .photo-container img {
      width: 130px; /* 缩小照片尺寸 */
      height: 180px;
      object-fit: cover;
      border: 1px solid #f0f0f0;
    }

    .name-container {
      text-align: center;
      padding: 10px 0;
      margin: 15px;
    }

    .name-container h1 {
      font-size: calc(var(--base-font-size) * 1.8); /* 调整字体大小 */
      font-weight: bold;
      word-break: break-word; /* 允许长名字换行 */
    }

    /* 右侧栏样式 */
    .right-column {
      padding: 15px;
      overflow: hidden; /* 防止内容溢出 */
      box-sizing: border-box; /* 确保padding不会增加宽度 */
    }

    /* 通用部分样式 */
    .section {
      margin-bottom: 10px;
      overflow: hidden; /* 防止内容溢出 */
    }

    .section-tile-and-line {
      display: grid;
      grid-template-columns: auto 1fr; /* 标题自适应宽度，线条占据剩余空间 */
      align-items: center;
      background-color: #ffffff;
      margin-bottom: 10px;
      gap: 10px; /* 设置标题和线条之间的间距 */
    }

    .section-title {
      display: flex;
      align-items: center;
      padding: 8px 15px;
      background-color: var(--theme-color);
      color: white;
      border-radius: 4px;
      width: auto; /* 自动宽度 */
    }

    .section-title i {
      margin-right: 10px;
      font-size: calc(var(--base-font-size) * 1.2);
    }

    .section-title h2 {
      font-size: calc(var(--base-font-size) * 1.2);
      font-weight: normal;
      white-space: nowrap; /* 防止文字换行 */
    }

    .section-title-line {
      height: 1px;
      background-color: var(--theme-color);
      margin-right: 10px;
    }

    .section-content {
      padding: 0 0px;
      color: black;
      word-wrap: break-word; /* 允许长词换行 */
      overflow-wrap: break-word; /* 现代浏览器支持 */
    }

    .left-column .section-content {
      font-size: min(var(--base-font-size), var(--max-font-size));
      line-height: 1.0;
    }

    .left-column #contact-section-content {
      font-size: min(var(--base-font-size), var(--max-font-size));
      line-height: 1.0;
    }

    .left-column .section-content p {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 20px; /* 添加行间距 */
    }

    .left-column .section-content p span:first-child {
      width: 75px;
      font-weight: bold;
      white-space: nowrap; /* 防止字段名称换行 */
      flex-shrink: 0; /* 防止字段名称被压缩 */
    }

    #contact-section-content p span:first-child {
      width: auto;
      font-weight: bold;
      white-space: nowrap; /* 防止字段名称换行 */
      flex-shrink: 0; /* 防止字段名称被压缩 */
    }

    .left-column-strip {
      width: 100%;
      height: 18px;
      background-color: var(--theme-color);
      border-radius: 4px;
    }

    .left-column-title {
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 8px 15px;
      margin-bottom: 10px;
    }

    /* 替换i元素样式为img元素样式 */
    .left-column-title i {
      margin-right: 10px;
      font-size: calc(var(--base-font-size) * 1.2);
    }

    .left-column-title h2 {
      font-size: calc(var(--base-font-size) * 1.2);
      font-weight: bold;
      color: black;
    }

    /* 教育背景样式 */
    .three-column-header {
      display: grid;
      grid-template-columns: 0.8fr 1fr 0.8fr;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 5px; /* 添加列间距 */
      margin-bottom: 10px;
      font-weight: bold;
    }

    .education-item .experience-item .internship-item {
      margin-bottom: 10px;
    }

    .education-courses {
      line-height: var(--spacing);
      margin-bottom: 10px;
    }

    /* 在校经历样式 */
    /* .experience-item {
      margin-bottom: 5px;
      position: relative;
    } */

    /* .experience-item:before {
      content: "";
      position: absolute;
      left: 0;
      top: 5px;
      width: 8px;
      height: 8px;
      background-color: var(--theme-color);
      border-radius: 50%;
    } */

    /* 奖项荣誉样式 兴趣爱好样式 技能证书样式 */
    /* .award-item, .skill-item, .interest-item {
        padding-left: 20px;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        flex-wrap:wrap;
     } */
    .award-item div, .skill-item div, .interest-item div {
      /* margin-right: 25px; */
      font-weight: normal;
    }


    /* 自我评价样式 */
    .evaluation-item {
      margin-bottom: 10px;
      position: relative;
      padding-left: 20px;
    }

    .evaluation-item:before {
      content: "";
      position: absolute;
      left: 0;
      top: 5px;
      width: 8px;
      height: 8px;
      background-color: var(--theme-color);
      border-radius: 50%;
    }

    /* 隐藏空模块 */
    .hidden {
      display: none;
    }

    /* 确保内容超长时可以自动换行 */
    p, h1, h2, h3, span {
      max-width: 100%;
      overflow-wrap: break-word;
      word-wrap: break-word;
    }

    /* 列表样式 */
    .item-list {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    .item-list li {
      margin-bottom: 10px;
      position: relative;
      padding-left: 20px;
    }

    .item-list li:before {
      content: "";
      position: absolute;
      left: 0;
      top: 5px;
      width: 8px;
      height: 8px;
      background-color: var(--theme-color);
      border-radius: 50%;
    }

    /* 打印媒体查询 */
    @page {
      size: A4;
      margin: 0;
      /* padding: 3mm 3mm; */
    }

    @media print {
      .resume-container {
        box-shadow: none;
        margin: 0;
        overflow: hidden;
      }

      .section-tile-and-line, .section{
        break-inside: avoid;
        page-break-inside: avoid;
      }
      /* .section-content {
        orphans: 3;
        widows: 3;
      } */
    }
  </style>
</head>

<body>
  <div class="resume-container">
    <!-- 简历头部 -->
    <header class="resume-title">
      <div class="header-icons">
        <!-- 使用SVG use标签复用图标并控制大小 -->
        <img class="icon-1" src="{{ base_url|default('') }}/static/resume-images/templateA02/chiLun.png" alt="图标" onerror="this.style.display='none';" />
        <img class="icon-2" src="{{ base_url|default('') }}/static/resume-images/templateA02/chiLun.png" alt="图标" onerror="this.style.display='none';" />
        <img class="icon-3" src="{{ base_url|default('') }}/static/resume-images/templateA02/chiLun.png" alt="图标" onerror="this.style.display='none';" />
        <img class="icon-4" src="{{ base_url|default('') }}/static/resume-images/templateA02/chiLun.png" alt="图标" onerror="this.style.display='none';" />
      </div>
      <div class="header-image-right">
        <img src="{{ base_url|default('') }}/static/resume-images/templateA02/personal_resume.png" alt="图标" onerror="this.style.display='none';" />
      </div>
    </header>

    <div class="content">
      <!-- 左侧栏 -->
      <div class="left-column">
        <!-- 照片区域 -->
        {% if resume.basicInfo.photoUrl %}
        <div class="photo-container">
          <img id="avatar" src="{{ resume.basicInfo.photoUrl }}" alt="个人照片" loading="eager" onerror="this.onerror=null; this.src='data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22130%22%20height%3D%22180%22%20viewBox%3D%220%200%20130%20180%22%3E%3Crect%20fill%3D%22%23f0f0f0%22%20width%3D%22130%22%20height%3D%22180%22%2F%3E%3Ctext%20fill%3D%22%23888%22%20font-family%3D%22sans-serif%22%20font-size%3D%2220%22%20dy%3D%22.3em%22%20text-anchor%3D%22middle%22%20x%3D%2265%22%20y%3D%2290%22%3E照片%3C%2Ftext%3E%3C%2Fsvg%3E'">
        </div>
        {% endif %}

        <!-- 姓名 -->
        <div class="name-container">
          <h1 id="name">{{ resume.basicInfo.name }}</h1>
        </div>

        <!-- 联系方式 -->
        <div class="section {% if hide_basic_info %}hidden{% endif %}" id="contact-section">
          <div class="left-column-strip"></div>
          <div class="left-column-title">
            <!-- 使用SVG use标签复用联系方式图标 -->
            <!-- <img class="icon-1" src="{{ base_url|default('') }}/static/resume-images/templateA02/tel.svg" alt="联系方式图标" onerror="this.style.display='none';" /> -->
            <i class="fas fa-mobile-screen"></i>

            <h2>联系方式</h2>
          </div>
          <div class="section-content" id="contact-section-content">
            {% if resume.basicInfo.phone %}<p><span>电话：</span><span id="phone">{{ resume.basicInfo.phone }}</span></p>{% endif %}
            {% if resume.basicInfo.email %}<p><span>邮箱：</span><span id="email">{{ resume.basicInfo.email }}</span></p>{% endif %}
            {% if resume.basicInfo.wechat %}<p><span>微信：</span><span id="wechat">{{ resume.basicInfo.wechat }}</span></p>{% endif %}
          </div>
        </div>

        <!-- 个人信息 -->
        <div class="section {% if hide_basic_info %}hidden{% endif %}" id="personal-info-section">
          <div class="left-column-strip"></div>
          <div class="left-column-title">
            <!-- 使用SVG use标签复用个人信息图标 -->
            <!-- <img class="icon-1" src="{{ base_url|default('') }}/static/resume-images/templateA02/info.svg" alt="个人信息图标" onerror="this.style.display='none';" /> -->
            <i class="fas fa-id-card"></i>
            <h2>个人信息</h2>
          </div>
          <div class="section-content">
            {% if resume.basicInfo.gender %}<p><span>性别：</span><span id="gender">{{ resume.basicInfo.gender }}</span></p>{% endif %}
            {% if resume.basicInfo.age %}<p><span>年龄：</span><span id="age">{{ resume.basicInfo.age }}</span></p>{% endif %}
            {% if resume.basicInfo.nation %}<p><span>民族：</span><span id="ethnicity">{{ resume.basicInfo.nation }}</span></p>{% endif %}
            {% if resume.basicInfo.hometown %}<p><span>籍贯：</span><span id="hometown">{{ resume.basicInfo.hometown }}</span></p>{% endif %}
            {% if resume.basicInfo.city %}<p><span>现居：</span><span id="current-location">{{ resume.basicInfo.city }}</span></p>{% endif %}
            {% if resume.basicInfo.marriage %}<p><span>婚姻状况：</span><span id="marriage">{{ resume.basicInfo.marriage }}</span></p>{% endif %}
            {% if resume.basicInfo.birthday %}<p><span>生日：</span><span id="birthday">{{ resume.basicInfo.birthday }}</span></p>{% endif %}
            {% if resume.basicInfo.height %}<p><span>身高：</span><span id="height">{{ resume.basicInfo.height }}</span></p>{% endif %}
            {% if resume.basicInfo.weight %}<p><span>体重：</span><span id="weight">{{ resume.basicInfo.weight }}</span></p>{% endif %}
            {% if resume.basicInfo.educationLevel %}<p><span>学历：</span><span id="education-level">{{ resume.basicInfo.educationLevel }}</span></p>{% endif %}
            {% if resume.basicInfo.politics %}<p><span>政治面貌：</span><span id="political-status">{{ resume.basicInfo.politics }}</span></p>{% endif %}
          </div>
        </div>
      </div>


      <!-- 右侧栏 -->
      <div class="right-column">
        {% for module in ordered_modules %}
          <!-- 求职意向 -->
          {% if module.key == 'jobIntention' and module.data %}
          <div class="section" id="job-intention-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-bullseye"></i>
                <h2>求职意向</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <!-- <div class="section-content" style="display: flex; gap:20px; flex-wrap:wrap;"> -->
            <div class="section-content" style="display: grid; grid-template-columns: repeat(2, 1fr); gap:20px; flex-wrap:wrap;">
              {% if module.data.position %}<p><span>期望职位：</span><span id="position">{{ module.data.position }}</span></p>{% endif %}
              {% if module.data.salary %}<p><span>期望薪资：</span><span id="salary">{{ module.data.salary }}</span></p>{% endif %}
              {% if module.data.city %}<p><span>期望城市：</span><span id="job-city">{{ module.data.city }}</span></p>{% endif %}
              {% if module.data.status %}<p><span>求职状态：</span><span id="job-status">{{ module.data.status }}</span></p>{% endif %}
            </div>
          </div>
          {% endif %}


          <!-- 教育背景 -->
          {% if module.key == 'education' and module.data %}
          <div class="section" id="education-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-graduation-cap"></i>
                <h2>教育背景</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="education-content">
              {% if module.data and module.data|length > 0 %}
                {% for edu in module.data %}
                  <div class="education-item">
                    <div class="three-column-header">
                      <span>{{ edu.startDate or '' }}-{{ edu.endDate or '' }}</span>
                      <span>{{ edu.school or '' }}</span>
                      <span>{{ edu.major or '' }}（{{ edu.degree or '' }}）</span>
                    </div>
                    <div class="education-courses">
                      {% if edu.description %}<p><span>主修课程：</span><span>{{ edu.description }}</span></p>{% endif %}
                    </div>
                  </div>
                {% endfor %}
              {% endif %}
            </div>
          </div>
          {% endif %}

          <!-- 实习经历 -->
          {% if module.key == 'internship' and module.data %}
          <div class="section" id="internship-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-id-badge"></i>
                <h2>实习经历</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="internship-content">
              {% if module.data and module.data|length > 0 %}
                {% for item in module.data %}
                  <div class="internship-item">
                    <div class="three-column-header">
                      <span>{{ item.startDate }}-{{ item.endDate }}</span>
                      <span>{{ item.company }}</span>
                      <span>{{ item.position }}</span>
                    </div>
                    <div class="education-courses">
                      {% if item.content %}<p><span>{{ item.content }}</span></p>{% endif %}
                    </div>
                  </div>
                {% endfor %}
              {% endif %}
            </div>
          </div>
          {% endif %}

          <!-- 在校经历 -->
          {% if module.key == 'school' and module.data %}
          <div class="section" id="school-experience-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-school"></i>
                <h2>在校经历</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="school-experience-content">
              {% if module.data and module.data|length > 0 %}
                {% for item in module.data %}
                  <div class="education-item">
                    <div class="three-column-header">
                      <span>{{ item.startDate }}-{{ item.endDate }}</span>
                      <span class="empty-span"></span>  
                      <span>{{ item.role or '' }}</span>
                    </div>
                    <div class="education-courses">
                      {% if item.content %}<p><span>{{ item.content }}</span></p>{% endif %}
                    </div>
                  </div>
                {% endfor %}
              {% endif %}

            </div>
          </div>
          {% endif %}

          <!-- 工作经历 -->
          {% if module.key == 'work' and module.data %}
          <div class="section" id="work-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-briefcase"></i>
                <h2>工作经历</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="work-content">
              {% if module.data and module.data|length > 0 %}
                {% for item in module.data %}
                  <div class="education-item">
                    <div class="three-column-header">
                      <span>{{ item.startDate }}-{{ item.endDate }}</span>
                      <span>{{ item.company }}</span>  
                      <span>{{ item.position }}</span>
                    </div>
                    <div class="education-courses">
                      {% if item.description %}<p><span>{{ item.description }}</span></p>{% endif %}
                    </div>
                  </div>
                {% endfor %}
              {% endif %}
            </div>
          </div>
          {% endif %}

          <!-- 项目经历 -->
          {% if module.key == 'project' and module.data %}
          <div class="section" id="project-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-project-diagram"></i>
                <h2>项目经历</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="project-content">
              {% if module.data and module.data|length > 0 %}
                {% for item in module.data %}
                  <div class="project-item">
                    <div class="three-column-header">
                      <span>{{ item.startDate }}-{{ item.endDate }}</span>
                      <span>{{ item.projectName }}</span>  
                      <span>{{ item.role }}</span>
                    </div>
                    <div class="education-courses">
                      {% if item.description %}<p><span>{{ item.description }}</span></p>{% endif %}
                    </div>
                  </div>
                {% endfor %}
              {% endif %}
            </div>
          </div>
          {% endif %}

          <!-- 技能证书 -->
          {% if module.key == 'skills' and module.data %}
          <div class="section" id="skills-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-certificate"></i>
                <h2>技能证书</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="skills-content">
              <div class="skill-item three-column-header">
                {% for skill in module.data %}
                  <div>{{ skill }}</div>
                {% endfor %}
              </div>
            </div>
          </div>
          {% endif %}

          <!-- 奖项荣誉 -->
          {% if module.key == 'awards' and module.data %}
          <div class="Section" id="awards-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-award"></i>
                <h2>奖项荣誉</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="awards-content">
              <div class="award-item three-column-header">
                {% for award in module.data %}
                  <div>{{ award }}</div>
                {% endfor %}
              </div>
            </div>
          </div>
          {% endif %}

          <!-- 兴趣爱好 -->
          {% if module.key == 'interests' and module.data %}
          <div class="section" id="interests-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-heart"></i>
                <h2>兴趣爱好</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="interests-content">
              <div class="interest-item three-column-header">
                {% for interest in module.data %}
                  <div>{{ interest }}</div>
                {% endfor %}
              </div>
            </div>
          </div>
          {% endif %}

          <!-- 自我评价 -->
          {% if module.key == 'evaluation' and module.data %}
          <div class="section" id="self-evaluation-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-comment"></i>
                <h2>自我评价</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content" id="self-evaluation-content">
              {% for eval_item in module.data %} {# Assuming evaluation is List[Dict[str, Any]] with 'content' key #}
              <div class="long-text-item">
                <p>{{ eval_item.content | safe if eval_item.content else eval_item | safe }}</p> {# Handle if item itself is a string #}
              </div>
              {% endfor %}



            </div>
          </div>
          {% endif %}

          <!-- 自定义模块1 -->
          {% if module.key == 'custom1' and module.data %}

          <div class="section" id="custom1-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-star"></i>
                <h2>{% if module.data[0] and module.data[0].customName %}{{ module.data[0].customName }}{% else %}自定义模块1{% endif %}</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content">
              {% if module.data and module.data|length > 0 %}
                {% for item in module.data %}
                  <div class="custom-item experience-item">
                    <div class="three-column-header">
                      <p>{{ item.startDate or '' }} {% if item.endDate %}- {{ item.endDate }}{% endif %}</p>
                      <p class="empty_p"></p>
                      <p>{{ item.role or '' }}</p>
                    </div>
                    <div class="education-courses">
                      <p>{{ item.content or '' }}</p>
                    </div>
                  </div>
                {% endfor %}
              {% endif %}
            </div>
          </div>
          {% endif %}

          <!-- 自定义模块2 -->
          {% if module.key == 'custom2' and module.data %}
          <div class="section {% if hide_custom2 %}hidden{% endif %}" id="custom2-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-star"></i>
                <h2>{% if module.data[0] and module.data[0].customName %}{{ module.data[0].customName }}{% else %}自定义模块2{% endif %}</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content">
              {% if module.data and module.data|length > 0 %}
                {% for item in module.data %}
                  <div class="custom-item experience-item">
                    <div class="three-column-header">
                      <p>{{ item.startDate or '' }} {% if item.endDate %}- {{ item.endDate }}{% endif %}</p>
                      <p class="empty_p"></p>
                      <p>{{ item.role or '' }}</p>
                    </div>
                    <div class="education-courses">
                      <p>{{ item.content or '' }}</p>
                    </div>
                  </div>
                {% endfor %}
              {% endif %}
            </div>
          </div>
          {% endif %}

          <!-- 自定义模块3 -->
          {% if module.key == 'custom3' and module.data %}
          <div class="section {% if hide_custom3 %}hidden{% endif %}" id="custom3-section">
            <div class="section-tile-and-line">
              <div class="section-title">
                <i class="fas fa-star"></i>
                <h2>{% if module.data[0] and module.data[0].customName %}{{ module.data[0].customName }}{% else %}自定义模块3{% endif %}</h2>
              </div>
              <div class="section-title-line"></div>
            </div>
            <div class="section-content">
              {% if module.data and module.data|length > 0 %}
                {% for item in module.data %}
                  <div class="custom-item experience-item">
                    <div class="three-column-header">
                      <p>{{ item.startDate or '' }} {% if item.endDate %}- {{ item.endDate }}{% endif %}</p>
                      <p class="empty_p"></p>
                      <p>{{ item.role or '' }}</p>
                    </div>
                    <div class="education-courses">
                      <p>{{ item.content or '' }}</p>
                    </div>
                  </div>
                {% endfor %}
              {% endif %}
            </div>
          </div>
          {% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
</body>
</html>